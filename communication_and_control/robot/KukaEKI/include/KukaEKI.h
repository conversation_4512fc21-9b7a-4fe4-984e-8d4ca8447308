#pragma once

#include <string>
#include <cmath>
#include <functional>
#include <memory>

namespace algorithm
{
    namespace kuka_eki
    {

        /**
         * \brief 上行信息（下位机-›上位机）
         * */
        struct SRobotSendInfo
        {
            std::string cmdName;    // 控制指令 ID
            int id;                 // 工件 ID
            float axis_act[12];
            float pos_act[6];       // 末端工具位姿, (x,y,z,a,b,c)
            int s;
            int t;
            float e[6];
            std::string param[5];   // 扩展参数
        };

        /**
         * @brief 下行位姿（上位机-›下位机）
         * */
        struct ResultPos
        {
            double X;
            double Y;
            double Z;
            double A;
            double B;
            double C;

            bool operator==(const ResultPos& pose) const
            {
                return (fabs(X - pose.X) < 1e-6 && fabs(Y - pose.Y) < 1e-6 && fabs(Z - pose.Z) < 1e-6 && fabs(A - pose.A) < 1e-6 && fabs(B - pose.B) < 1e-6 && fabs(C - pose.C) < 1e-6);
            }
        };

        /**
         * @brief 扩展参数
         * */
        struct MotionParam
        {
            std::string Para1 = "0";
            std::string Para2 = "0";
            std::string Para3 = "0";
            std::string Para4 = "0";
            std::string Para5 = "0";
        };

        /**
         * @brief 控制指令ID
         * */
        enum CmdName
        {
            lin_single_cartesian_relative_motion = 10,
            ptp_single_cartesian_relative_motion = 11,
            lin_single_cartesian_absolute_motion = 12,
            ptp_single_cartesian_absolute_motion = 13,

            ptp_single_joint_relative_motion = 14,
            ptp_single_joint_absolute_motion = 15,

            lin_continuous_cartesian_relative_motion = 16,
            lin_continuous_cartesian_absolute_motion = 17,

            ptp_continuous_cartesian_absolute_motion = 25,

            ptp_continuous_joint_relative_motion = 18,
            ptp_continuous_joint_absolute_motion = 19,

            io_output = 20,
            io_input = 21,

            customize_lin_single_cartesian_absolute_motion = 22,
            customize_ptp_single_cartesian_absolute_motion = 23,
            customize_lin_continuous_cartesian_absolute_motion = 24,
            customize_ptp_continuous_cartesian_absolute_motion = 26,

            setting_robot_motion_params = 27,

            lin_continuous_cartesian_absolute_motion_warning = 81, // 告警下发
            error_code = 99,
            change_workpiece_id = 900,
            auto_calibration_starting = 990,
            auto_calibration_running = 991,
            auto_calibration_stoping = 992,

            auto_intrinsic_calibration = 993,

            manual_calibration = 994,

            setting_auto_report_robot_pose = 995,
        };

        /**
         * @brief 处理机器人上行报文的回调函数，参数为：1.结构化报文 2.原始报文
         * */
        typedef std::function<void(const SRobotSendInfo &, const std::string &)> ReceiveRobotMsgCallbackType;

        /**
         * \brief 处理连接状态变更的回调函数，参数为 1.客户端IP:端口、2.连接or断开
         * */
        typedef std::function<void(const std::string &, const bool &)> ConnectStatusChangeCallbackType;
    }

    /**
     * \brief [KukaEKI]的配置
     * */
    struct KukaEKIConfigurable
    {
        /** \brief 上位机监听IP，为空默认使用0.0.0.0*/
        std::string vision_ip;
        /** \brief 上位机监听PORT*/
        int vision_port;
        /** \brief kss版本是否小于 9.1.2 */
        bool is_kss_version_less_9_1_2;
    };

    using namespace kuka_eki;

    /**
     * @brief KukaEKI服务端（上位机）
     * \ingroup device_control
     * */
    class KukaEKI
    {
    public:
        /// @brief 构造函数
        /// @param msg_callback 处理机器人上行报文的回调函数
        /// @param status_callback 处理连接状态变更的回调函数
        KukaEKI(ReceiveRobotMsgCallbackType msg_callback, ConnectStatusChangeCallbackType status_callback);
        ~KukaEKI();

        /// @brief 初始化配置
        void init_config(const KukaEKIConfigurable &config);

        /**
         * @brief 下发控制命令
         * @param[in] cmdName 命令字
         * @param[in] poses 位姿列表
         * @param[in] params 扩展参数
         * @param[in] id 对应EKI协议的StatusCode
         * @param[in] simulator 模拟发送命令
         * @return 下发的原始报文
         * */
        std::string send_msg(const CmdName &cmdName, const std::vector<ResultPos> &poses = {}, const MotionParam &params = {}, int id = 0, bool simulator = false);

        /**
         * @brief 把给定参数转化为原始的下发报文
         * @param[in] cmdName 命令字
         * @param[in] poses 位姿列表
         * @param[in] params 扩展参数
         * @param[in] id 对应EKI协议的StatusCode
         * @return 原始报文
         * */
        std::string serialize4Print(const CmdName &cmdName, const std::vector<ResultPos> &poses, const MotionParam &params = {}, int id = 0);

        /** \brief 获取已连接的客户端数量*/
        int getConnectedClientNum();

    private:
        struct Impl;
        std::unique_ptr<Impl> pImpl;
    };

    /** @}*/
};
