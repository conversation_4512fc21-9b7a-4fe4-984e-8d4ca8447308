### 硬件环境
主板         : Jetson Orin NX8G
model name	: Cortex-A78AE
GPU		    : Orin (nvgpu)


### 软件环境
OS		    : Ubuntu 22.04.5 LTS
Docker 	    : 27.2.0
Cuda 		: 12.2
OpenCV		: 4.5.4 

### 测试用例
1. 开启eki服务，等待客户端连接(此处采样socket工具连接和发送消息)
2. 客户端连接后接收客户端消息和向客户端发送消息
3. 测试发送和接收字符串消息，查看发送和接收是否相同


### 测试结果
- socket 工具发送消息截图
![image](send.png)

```text
节点接收数据日志
[2024-11-29 15:05:32.138] [warning] [KukaEKITest.cpp:20] Connection status changed: 10.33.244.54:57354 connected
[2024-11-29 15:08:28.385] [warning] [KukaEKITest.cpp:24] Receive Message: <RobotSend>
  <CmdName>Grab_Ready</CmdName>
  <ID>1</ID>
  <AXIS_ACT>
    <A1>0.000000</A1>
    <A2>-90.000000</A2>
    <A3>90.000000</A3>
    <A4>0.000000</A4>
    <A5>0.000000</A5>
    <A6>0.000000</A6>
    <E1>0.000000</E1>
    <E2>0.000000</E2>
    <E3>0.000000</E3>
    <E4>0.000000</E4>
    <E5>0.000000</E5>
    <E6>0.000000</E6>
  </AXIS_ACT>
  <POS_ACT_E6POS X="1765.000000" Y="0.000000" Z="1784.000000" A="0.000000" B="90.000000" C="0.000000">
    <S>2</S>
    <T>2</T>
    <E1>0.000000</E1>
    <E2>0.000000</E2>
    <E3>0.000000</E3>
    <E4>0.000000</E4>
    <E5>0.000000</E5>
    <E6>0.000000</E6>
  </POS_ACT_E6POS>
  <Para1></Para1>
  <Para2></Para2>
  <Para3></Para3>
  <Para4></Para4>
  <Para5></Para5>
</RobotSend>
```

- 向工具发送消息
![image](send_to_robot.png)
- 接收结果(模拟数据不发送)
![image](sock_recv.png)









