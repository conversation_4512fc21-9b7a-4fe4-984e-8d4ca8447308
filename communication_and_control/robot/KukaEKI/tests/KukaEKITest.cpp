#include "KukaEKI.h"
#include <spdlog/spdlog.h>
#include <gtest/gtest.h>
#include <chrono>
#include <thread>

using namespace algorithm::kuka_eki;

class KukaEKITest : public ::testing::Test
{
protected:
    std::unique_ptr<algorithm::KukaEKI> KukaEKI_ptr;
    std::unique_ptr<ReceiveRobotMsgCallbackType> msg_callback_ptr;
    std::unique_ptr<ConnectStatusChangeCallbackType> status_callback_ptr;

    void SetUp() override
    {
        auto status_callback = [](const std::string &address, const bool &connected)
        {
            SPDLOG_WARN("Connection status changed: {} {}",address,(connected ? "connected" : "disconnected"));
        };
        auto msg_callback = [](const SRobotSendInfo &message, const std::string &rawMessage)
        {
            SPDLOG_WARN("Receive Message: {}",rawMessage);
        };

        status_callback_ptr = std::make_unique<ConnectStatusChangeCallbackType>(status_callback);
        msg_callback_ptr = std::make_unique<ReceiveRobotMsgCallbackType>(msg_callback);
        KukaEKI_ptr = std::make_unique<algorithm::KukaEKI>(*msg_callback_ptr, *status_callback_ptr);
    }
};

TEST_F(KukaEKITest, UnitTest)
{
    algorithm::KukaEKIConfigurable config;
    config.vision_port = 8899;
    KukaEKI_ptr->init_config(config);
    getchar();
    KukaEKI_ptr->send_msg(CmdName::lin_continuous_cartesian_absolute_motion);
    SPDLOG_DEBUG("=============================");
    KukaEKI_ptr->send_msg(CmdName::lin_continuous_cartesian_absolute_motion, {{1.0, 2.0, 3.0, 180.0, 90.0, 45.0}});
    SPDLOG_DEBUG("=============================");
    KukaEKI_ptr->send_msg(CmdName::lin_continuous_cartesian_absolute_motion, {{2.0, 2.0, 3.0, 180.0, 90.0, 45.0}}, {"", "", "", "error:1"});
    std::this_thread::sleep_for(std::chrono::seconds(1));
    KukaEKI_ptr->send_msg(CmdName::lin_continuous_cartesian_absolute_motion, {{3.0, 2.0, 3.0, 180.0, 90.0, 45.0}}, {"", "", "", "error:1"}, 1);
    SPDLOG_DEBUG("=============================");
    KukaEKI_ptr->send_msg(CmdName::lin_continuous_cartesian_absolute_motion, {{4.0, 2.0, 3.0, 180.0, 90.0, 45.0}}, {"", "", "", "error:1"}, 2, true);
    std::this_thread::sleep_for(std::chrono::seconds(2));
    SUCCEED();
}

TEST_F(KukaEKITest, Performance)
{
    SUCCEED();
}

int main(int argc, char *argv[])
{
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}