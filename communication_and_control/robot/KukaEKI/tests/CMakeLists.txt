cmake_minimum_required(VERSION 3.12.0)
project(KukaEKITest)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

find_package(spdlog REQUIRED)
#find_package(PCL REQUIRED) #1.10
#find_package(OpenCV REQUIRED)   #4.2.0
#find_package(Eigen3 REQUIRED) #3.3.7
find_package(GTest REQUIRED)

#add_definitions(${PCL_DEFINITIONS})

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../../common
#    ${PCL_INCLUDE_DIRS}
#    ${OpenCV_INCLUDE_DIRS}
#    ${EIGEN3_INCLUDE_DIR}
)


add_executable(${PROJECT_NAME} KukaEKITest.cpp)

target_link_libraries(${PROJECT_NAME} PRIVATE gtest pthread spdlog::spdlog  libKukaEKI)
gtest_discover_tests(${PROJECT_NAME})
