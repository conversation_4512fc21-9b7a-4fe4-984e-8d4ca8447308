cmake_minimum_required(VERSION 3.12.0)

# project和目录名字保持一致
project(KukaEKI)

# 设置版本号
set(${PROJECT_NAME}_VERSION_MAJOR 1)
set(${PROJECT_NAME}_VERSION_MINOR 0)

set(CMAKE_INSTALL_PREFIX ${CMAKE_BINARY_DIR}/install)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

find_package(spdlog REQUIRED)
#find_package(PCL REQUIRED) #1.10
#find_package(OpenCV REQUIRED)   #4.2.0
#find_package(Eigen3 REQUIRED) #3.3.7

# 如果需要用到对于对应的库，请直接放开对应注释即可
# find_package(Open3D REQUIRED) #0.18.0
# find_package(onnxruntime REQUIRED)    #1.12.1
# find_package(OpenMP REQUIRED) #4.0.3
# find_package(yaml-cpp REQUIRED)   #0.6.2
# find_package(nlohmann_json REQUIRED)  #3.9.1
# find_package(stduuid REQUIRED)    #1.2.3   # 注意，这个不是类nix系统自带的那个uuid
# find_package(CUDA REQUIRED)   #11.4
# find_package(Threads REQUIRED)
# find_package(CURL REQUIRED)

#编译时开启所有级别的spdlog日志
add_compile_definitions(SPDLOG_ACTIVE_LEVEL=0)

# add_definitions(-DHAVE_CUDA)

# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")

if (NOT TARGET hv)
    add_subdirectory(../../../3rd/libhv libhv EXCLUDE_FROM_ALL)
endif()

set(DEPEND_LIBS
    spdlog::spdlog
    hv
    # ${PCL_LIBRARIES}
    # ${OpenCV_LIBRARIES}
    # ${EIGEN3_LIBRARIES}
    # ${Open3D_LIBRARIES}
    # ${onnxruntime_LIBRARIES}
    # ${OpenMP_CXX_LIBRARIES}
    # yaml-cpp
    # stduuid
    # ${CUDA_LIBRARIES}
    # ${CURL_LIBRARIES}
)


FILE(GLOB PROJECT_SOURCES
    src/*.cpp
#    src/socket/*.cpp
    src/KukaEkiXmlProtocol/*.cpp
    src/Markup/*.cpp
)


if(${BUILD_SHARED_LIBS})
    add_library(lib${PROJECT_NAME} SHARED ${PROJECT_SOURCES})
else()
    add_library(lib${PROJECT_NAME} STATIC ${PROJECT_SOURCES})
endif()

target_include_directories(lib${PROJECT_NAME} PUBLIC
    include/
)
file(GLOB hdr_FILES include/*.h include/*.hpp)
target_include_directories(lib${PROJECT_NAME} PRIVATE
    src/
#    3rd/libhv/include
)

target_link_libraries(lib${PROJECT_NAME} PRIVATE ${DEPEND_LIBS})

# 设置目标属性，添加 -fPIC 选项
set_target_properties(lib${PROJECT_NAME} PROPERTIES POSITION_INDEPENDENT_CODE ON)

if (CMAKE_CXX_COMPILE_ID STREQUAL "GNU")
    target_compile_options(lib${PROJECT_NAME} PRIVATE -Wall -Werror -Wno-effc++ -Wno-error=class-memaccess)
endif()

target_compile_definitions(lib${PROJECT_NAME} PRIVATE MARKUP_STL SPDLOG_ACTIVE_LEVEL=0)

set(PKG_NAME ${PROJECT_NAME})
if(NOT DEFINED ROOT_PKG_NAME)
    set(PKG_DEST_DIR ${PKG_NAME})
    set(LIB_INSTALL_DIR lib)
    set(HEADER_INSTALL_DIR include/${PROJECT_NAME})
    file(GLOB common_hdr_FILES ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/*.h ${CMAKE_CURRENT_SOURCE_DIR}/../../../common/*.hpp)
    list(APPEND hdr_FILES ${common_hdr_FILES})
    set(ROOT_PKG_DEF FALSE)
else()
    set(ROOT_PKG_DEF TRUE)
    set(PKG_DEST_DIR ${ROOT_PKG_NAME}/${PKG_NAME})
    set(LIB_INSTALL_DIR lib/${ROOT_PKG_NAME})
    set(HEADER_INSTALL_DIR include/${ROOT_PKG_NAME}/${PROJECT_NAME})
endif()
configure_file(cmake/config.cmake.in ${PROJECT_NAME}Config.cmake @ONLY)
install(TARGETS lib${PROJECT_NAME} ARCHIVE DESTINATION ${LIB_INSTALL_DIR} LIBRARY DESTINATION ${LIB_INSTALL_DIR})
install(FILES ${hdr_FILES} DESTINATION ${HEADER_INSTALL_DIR})
install(FILES ${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}Config.cmake DESTINATION lib/cmake/${PROJECT_NAME})

# 构建一个deb软件包
set(CPACK_GENERATOR "DEB")
set(CPACK_PACKAGE_NAME "${PROJECT_NAME}")
set(CPACK_PACKAGE_VERSION "${${PROJECT_NAME}_VERSION_MAJOR}.${${PROJECT_NAME}_VERSION_MINOR}")
set(CPACK_PACKAGE_CONTACT "Brave <<EMAIL>>")
set(CPACK_DEBIAN_PACKAGE_DESCRIPTION "KukaEKI")
include(CPack)

if(ENABLE_TESTING)
    enable_testing()
    add_subdirectory(tests)
endif()
