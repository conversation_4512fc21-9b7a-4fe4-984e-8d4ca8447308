﻿#pragma once

#include "KukaEKI.h"
#include "Markup/Markup.h"
#include <string>
#include <iostream>
#include <vector>


using namespace algorithm::kuka_eki;

class KukaEkiXmlProtocol
{
public:
    static CMarkup serialize(const CmdName &cmdName, const MotionParam &params = {}, int statusCode = 0);
    static CMarkup serialize(const CmdName &cmdName, const std::vector<ResultPos> &poses, int status_code);
    static CMarkup serialize(const CmdName &cmdName, const std::vector<ResultPos> &poses, const MotionParam &params = {}, int statusCode = 0);

    static void serialize4Print(const std::vector<ResultPos> &poses, CMarkup &xml);

    /**
     * @brief robot to pc protocol parser
     * @param xmlString:
     * @param nLen:
     */
    static SRobotSendInfo deserializaXmlString(const std::string &xmlString);

private:
    static bool serialize(const MotionParam &param, CMarkup &xml);
    static bool serialize(const ResultPos &poses, CMarkup &xml, size_t no = 0);
    //
};