﻿#include "KukaEKI.h"
#include "KukaEkiXmlProtocol/KukaEkiXmlProtocol.h"
#include <sstream>
#include <iomanip>

std::string double2string(double value)
{
    std::ostringstream stream;
    stream << std::fixed << std::setprecision(3) << value;
    return stream.str();
}

using namespace algorithm::kuka_eki;


CMarkup KukaEkiXmlProtocol::serialize(const CmdName &cmdName, const MotionParam &params, int statusCode)
{
    return serialize(cmdName, {{}}, params, statusCode);
}

CMarkup KukaEkiXmlProtocol::serialize(const CmdName &cmdName, const std::vector<ResultPos> &poses, int status_code)
{
    return serialize(cmdName, poses, {}, status_code);
}

CMarkup KukaEkiXmlProtocol::serialize(const CmdName &cmdName, const std::vector<ResultPos> &poses, const MotionParam &params, int statusCode)
{
    // NOTE: make sure is a validate cmdName
    if (cmdName < 10 || (cmdName > 27 && cmdName < 990 && cmdName != 99 && cmdName != 81 && cmdName != 900) || cmdName > 995)
    {
        throw std::invalid_argument("unsupport CmdName: " + std::to_string(cmdName));
    }
    CMarkup xml;
    xml.AddElem("VisionResult");
    xml.AddChildElem("CmdName", cmdName);

    ResultPos empty_pose;
    bool use_new_eki = poses.size() > 1 && poses[0] == empty_pose;
    size_t i = use_new_eki ? 1 : 0;

    // ResultPos
    for (; i < poses.size(); ++i) 
    {
        auto& pose = poses[i];
        serialize(pose, xml, use_new_eki ? i : 0);
    }
    xml.AddChildElem("StatusCode", statusCode);

    // MotionParam
    serialize(params, xml);

    return xml;
}

bool KukaEkiXmlProtocol::serialize(const ResultPos &pose, CMarkup &xml, size_t no)
{
    if (no > 0) {
        xml.AddChildElem("ResultPos" + std::to_string(no));
    } else {
        xml.AddChildElem("ResultPos");
    }
    xml.IntoElem();
    xml.AddAttrib("X", double2string(pose.X));
    xml.AddAttrib("Y", double2string(pose.Y));
    xml.AddAttrib("Z", double2string(pose.Z));
    xml.AddAttrib("A", double2string(pose.A));
    xml.AddAttrib("B", double2string(pose.B));
    xml.AddAttrib("C", double2string(pose.C));
    xml.OutOfElem();
    return true;
}

bool KukaEkiXmlProtocol::serialize(const MotionParam &param, CMarkup &xml)
{
    xml.AddChildElem("Para1", param.Para1);
    xml.AddChildElem("Para2", param.Para2);
    xml.AddChildElem("Para3", param.Para3);
    xml.AddChildElem("Para4", param.Para4);
    xml.AddChildElem("Para5", param.Para5);
    return true;
}

SRobotSendInfo KukaEkiXmlProtocol::deserializaXmlString(const std::string &xmlString)
{
    // cout << "deserializaXmlString begin" << endl;
    CMarkup xml;
    xml.SetDoc(xmlString);
    xml.ResetMainPos();
    SRobotSendInfo info;
    xml.IntoElem();
    if (xml.FindElem("RobotSend"))
    {
        xml.IntoElem();

        if (xml.FindElem("CmdName"))
        {
            info.cmdName = xml.GetData();
        }
        if (xml.FindElem("ID"))
        {
            info.id = atoi(xml.GetData().c_str());
        }
        if (xml.FindElem("AXIS_ACT"))
        {

            xml.IntoElem();
            for (size_t i = 0; i < 12; i++)
            {
                char cchTmp[2] = {0};
                if (i < 6)
                {
                    cchTmp[0] = 'A';
                    cchTmp[1] = 0x30 + i + 1;
                }
                else
                {
                    int ii = i % 6;
                    cchTmp[0] = 'E';
                    cchTmp[1] = 0x30 + ii + 1;
                }

                std::string strNode = std::string(cchTmp);
                if (xml.FindElem(strNode))
                {
                    info.axis_act[i] = atof(xml.GetData().c_str());
                }
            }
            xml.OutOfElem();
        }

        if (xml.FindElem("POS_ACT_E6POS"))
        {
            int nIndex = 0;
            info.pos_act[nIndex++] = atof(xml.GetAttrib("X").c_str());
            info.pos_act[nIndex++] = atof(xml.GetAttrib("Y").c_str());
            info.pos_act[nIndex++] = atof(xml.GetAttrib("Z").c_str());
            info.pos_act[nIndex++] = atof(xml.GetAttrib("A").c_str());
            info.pos_act[nIndex++] = atof(xml.GetAttrib("B").c_str());
            info.pos_act[nIndex++] = atof(xml.GetAttrib("C").c_str());
            // cout << "strA = " << info.pos_act[3] << endl;

            xml.IntoElem();
            if (xml.FindElem("S"))
            {
                std::string strS = xml.GetData();
            }
            if (xml.FindElem("T"))
            {

                std::string strT = xml.GetData();
                /// cout << "T = " << strT << endl;
            }
            for (size_t i = 0; i < 6; ++i)
            {
                char cchTmp[2] = {0};
                if (i < 6)
                {
                    cchTmp[0] = 'E';
                    cchTmp[1] = 0x30 + i + 1;
                }

                std::string strNode = std::string(cchTmp);
                if (xml.FindElem(strNode))
                {
                    info.e[i] = atof(xml.GetData().c_str());
                    // cout << "e" << i << " = " << info.e[i] << endl;
                }
            }
            xml.OutOfElem();
        }

        for (size_t i = 0; i < 5; ++i)
        {
            std::string strPara = "Para";
            strPara += std::to_string(i + 1);
            if (xml.FindElem(strPara))
            {
                info.param[i] = xml.GetData();
                // cout << "param" << i << " = " << info.param[i] << endl;
            }
        }

        xml.OutOfElem();
    }
    xml.OutOfElem();
    return info;
}

void KukaEkiXmlProtocol::serialize4Print(const std::vector<ResultPos> &poses, CMarkup &xml)
{
    ResultPos empty_pose;
    bool use_new_eki = poses.size() > 1 && poses[0] == empty_pose;
    size_t i = use_new_eki ? 1 : 0;
    for (; i < poses.size(); ++i) {
        auto& pose = poses[i];
        xml.AddChildElem("ResultPos" + std::to_string(i));
        xml.IntoElem();
        xml.AddAttrib("X", double2string(pose.X));
        xml.AddAttrib("Y", double2string(pose.Y));
        xml.AddAttrib("Z", double2string(pose.Z));
        xml.AddAttrib("A", double2string(pose.A));
        xml.AddAttrib("B", double2string(pose.B));
        xml.AddAttrib("C", double2string(pose.C));
        xml.OutOfElem();
    }
}
