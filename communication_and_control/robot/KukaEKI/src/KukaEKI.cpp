﻿#include "KukaEKI.h"
#include "KukaEkiXmlProtocol/KukaEkiXmlProtocol.h"
#include "hv/TcpServer.h"
#include "hv/hlog.h"
#include <spdlog/spdlog.h>

namespace algorithm
{
    using namespace kuka_eki;
    struct KukaEKI::Impl {
        std::unique_ptr<hv::TcpServer> _tcpServer;
        ReceiveRobotMsgCallbackType _msg_callback;
        ConnectStatusChangeCallbackType _status_callback;
        KukaEKIConfigurable _config;
        int connectedClientNum = 0;

        Impl(ReceiveRobotMsgCallbackType msg_callback, ConnectStatusChangeCallbackType status_callback)
                : _msg_callback(msg_callback), _status_callback(status_callback) {
            // 禁用hv自带的log
            logger_set_level(hv_default_logger(), LOG_LEVEL_SILENT);
        }

        ~Impl() {
            if (_tcpServer) {
                _tcpServer->closesocket();
                _tcpServer->stop();
                _tcpServer.reset();
            }
        }

        void init_config(const KukaEKIConfigurable &config) {
            SPDLOG_DEBUG("Entering init_config.");
            if (_config.vision_ip != config.vision_ip || _config.vision_port != config.vision_port) {
                if (_tcpServer) {
                    _tcpServer->closesocket();
                    _tcpServer->stop();
                    _tcpServer.reset();
                }

                _tcpServer = std::make_unique<hv::TcpServer>();
                int listenfd = _tcpServer->createsocket(config.vision_port, config.vision_ip.data());
                if (listenfd >= 0) {
                    _tcpServer->onConnection = [=](const hv::SocketChannelPtr &channel) {
                        if (channel->isConnected()){
                            this->connectedClientNum++;
                        } else {
                            this->connectedClientNum--;
                        }
                        if (_status_callback) {
                            _status_callback(channel->peeraddr(), channel->isConnected());
                        }
                    };
                    _tcpServer->onMessage = [=](const hv::SocketChannelPtr &channel, hv::Buffer *buf) {
                        if (_msg_callback) {
                            std::string rawMessage{(const char *) buf->data(), buf->size()};
                            fixRawMsg(rawMessage);
                            SRobotSendInfo info = KukaEkiXmlProtocol::deserializaXmlString(rawMessage);
                            _msg_callback(info, rawMessage);
                        }
                    };
                    unpack_setting_t unpack_setting;
                    memset(&unpack_setting, 0, sizeof(unpack_setting_t));
                    unpack_setting.package_max_length = DEFAULT_PACKAGE_MAX_LENGTH;
                    unpack_setting.mode = UNPACK_BY_DELIMITER;
                    memcpy(unpack_setting.delimiter, delimiter.data(), delimiter.size());
                    unpack_setting.delimiter_bytes = delimiter.size();
                    _tcpServer->setUnpack(&unpack_setting);

//                _tcpServer->setThreadNum(2);
//                _tcpServer->setLoadBalance(LB_LeastConnections);
                    _tcpServer->start();
                } else {
                    _tcpServer.reset();
                    throw std::runtime_error("socket service start failed, port=" + std::to_string(config.vision_port));
                }
            }
            _config = config;
            SPDLOG_DEBUG("Exiting init_config!");
        }

        /*
         * hv最长支持8字符的包分隔符，EKI XML协议以</RobotSend>结束，超过了长度，因此这里只能从斜杠开始取8字符作为结束符
         * 收包后首先要判断前几个字符是否是上一帧未处理的结束符，并移除
         * 最后要补全结束符
         * */
        const std::string delimiter{"/RobotSe"};
        const std::string charsAfterDelimiter = "nd>";
        void fixRawMsg(std::string &rawMsg){
            auto pos = rawMsg.find(charsAfterDelimiter);
            if (pos == 0) {
                rawMsg.erase(0, charsAfterDelimiter.length());
            }
            rawMsg.append(charsAfterDelimiter);
        }

        std::string send_msg(const CmdName &cmdName, const std::vector<ResultPos> &poses, const MotionParam &motion_param, int id, bool simulator) {
            SPDLOG_DEBUG("Entering compute.");
            CMarkup xml = KukaEkiXmlProtocol::serialize(cmdName, poses, motion_param, id);
            std::string xmlMsg = xml.GetDoc();
            std::string rawMessage = send_to_robot(xmlMsg, simulator);
            SPDLOG_DEBUG("Exiting compute.");
            return rawMessage;
        }

        std::string send_to_robot(const std::string &strXml, const bool simulator) {
            SPDLOG_DEBUG("Entering send_to_robot.");
            SPDLOG_DEBUG("send msg = \n{}", strXml);
            if (simulator) {
                SPDLOG_INFO("simulator data, this will not real sent");
            } else {
                int send2ClientNum = _tcpServer->broadcast(strXml);
                if (!send2ClientNum) {
                    SPDLOG_ERROR("报文下发失败");
                }
            }

            SPDLOG_DEBUG("Exiting send_to_robot.");
            return strXml;
        }

        std::string serialize4Print(const CmdName &cmdName, const std::vector<ResultPos> &poses, const MotionParam &params, int id) {
            CMarkup xml = KukaEkiXmlProtocol::serialize(cmdName, {}, params, id);
            KukaEkiXmlProtocol::serialize4Print(poses, xml);
            return xml.GetDoc();
        }
    };

    KukaEKI::KukaEKI(ReceiveRobotMsgCallbackType msg_callback, ConnectStatusChangeCallbackType status_callback)
            :pImpl{std::make_unique<KukaEKI::Impl>(msg_callback, status_callback)}
    {

    }

    KukaEKI::~KukaEKI() = default;

    void KukaEKI::init_config(const KukaEKIConfigurable &config){
        pImpl->init_config(config);
    }

    std::string KukaEKI::send_msg(const CmdName &cmdName, const std::vector<ResultPos> &poses, const MotionParam &params, int id, bool simulator){
        return pImpl->send_msg(cmdName, poses, params, id, simulator);
    }

    std::string KukaEKI::serialize4Print(const CmdName &cmdName, const std::vector<ResultPos> &poses, const MotionParam &params, int id){
        return pImpl->serialize4Print(cmdName, poses, params, id);
    }

    int KukaEKI::getConnectedClientNum(){
        return pImpl->connectedClientNum;
    }

};
