get_filename_component(PACKAGE_PREFIX_DIR "${CMAKE_CURRENT_LIST_DIR}/../../../" ABSOLUTE)

set(@PKG_NAME@_INCLUDE_DIRS ${PACKAGE_PREFIX_DIR}/include/@PKG_DEST_DIR@)
find_library(@PKG_NAME@_LIBRARIES lib@PKG_NAME@ ${PACKAGE_PREFIX_DIR}/lib ${PACKAGE_PREFIX_DIR}/lib/@ROOT_PKG_NAME@)

# 导出变量以供外部项目使用
if(@PKG_NAME@_INCLUDE_DIRS AND @PKG_NAME@_LIBRARIES)
    list(APPEND @PKG_NAME@_INCLUDE_DIRS ${PACKAGE_PREFIX_DIR}/include/@ROOT_PKG_NAME@)
    set(@PKG_NAME@_FOUND TRUE)
endif()

# 提供一些附加信息
if(@PKG_NAME@_FOUND)
    message(STATUS "Found @PKG_NAME@: ${@PKG_NAME@_LIBRARIES}")
    if(@ROOT_PKG_DEF@)
        if(NOT TARGET @ROOT_PKG_NAME@::@PKG_NAME@)

            add_library(@ROOT_PKG_NAME@::@PKG_NAME@ UNKNOWN IMPORTED)

            set_target_properties(@ROOT_PKG_NAME@::@PKG_NAME@ PROPERTIES
                IMPORTED_LOCATION "${@PKG_NAME@_LIBRARIES}"
            )

            set_target_properties(@ROOT_PKG_NAME@::@PKG_NAME@ PROPERTIES
                INTERFACE_INCLUDE_DIRECTORIES "${@PKG_NAME@_INCLUDE_DIRS}"
            )

        endif()
    endif()
else()
    message(STATUS "Could not find @PKG_NAME@ library")
endif()
